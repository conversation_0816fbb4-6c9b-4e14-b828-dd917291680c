#!/usr/bin/env python3
"""
Debug script to test authentication and API calls.
Run this to debug authentication issues.
"""

import os
import logging
import requests
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_auth_flow():
    """Test the authentication flow step by step."""
    
    # Get credentials
    email = os.getenv("RTHINK_USER")
    password = os.getenv("RTHINK_PASS")
    
    if not email or not password:
        logger.error("Missing RTHINK_USER or RTHINK_PASS environment variables")
        return False
    
    session = requests.Session()
    base_url = "https://webapp.rethinkbehavioralhealth.com"
    
    headers = {
        "Content-Type": "application/json;charset=utf-8",
        "Accept": "application/json, text/plain, */*",
        "X-Application-Key": "74569e11-18b4-4122-a58d-a4b830aa12c4",
        "X-Origin": "Angular",
        "User-Agent": "Mozilla/5.0 (X11; Linux x86_64; rv:139.0) Gecko/139.0",
        "Origin": base_url,
        "Referer": f"{base_url}/Healthcare#/Login",
    }
    
    def get_token():
        for cookie in session.cookies:
            if any(k in cookie.name.upper() for k in ("XSRF", "ANTIFORGERY", "REQUESTVERIFICATIONTOKEN")):
                return cookie.value
        return None
    
    def with_token(headers_dict):
        token = get_token()
        if not token:
            raise Exception("No anti-forgery token found")
        return {**headers_dict, "X-XSRF-TOKEN": token}
    
    try:
        logger.info("Step 1: Getting initial session")
        resp1 = session.get(f"{base_url}/HealthCare", headers=headers)
        logger.info(f"Initial session: {resp1.status_code}")
        
        logger.info("Step 2: Getting authentication details")
        resp2 = session.post(
            f"{base_url}/HealthCare/SingleSignOn/GetAuthenticationDetail",
            json={"User": email},
            headers=with_token(headers)
        )
        logger.info(f"Auth details: {resp2.status_code}")
        
        logger.info("Step 3: Logging in")
        resp3 = session.post(
            f"{base_url}/HealthCare/User/Login",
            json={"User": email, "Password": password, "setPermissions": True},
            headers=with_token(headers)
        )
        logger.info(f"Login: {resp3.status_code}")
        
        logger.info("Step 4: Accessing scheduler")
        resp4 = session.get(
            f"{base_url}/core/scheduler/appointments",
            headers=with_token(headers)
        )
        logger.info(f"Scheduler access: {resp4.status_code}")
        
        logger.info("Step 5: Getting fresh token for API call")
        # Visit scheduler page again to get fresh token
        session.get(
            f"{base_url}/core/scheduler/appointments",
            headers=with_token(headers)
        )

        fresh_token = get_token()
        logger.info(f"Fresh token: {fresh_token[:20]}..." if fresh_token else "No token found")

        logger.info("Step 6: Testing API call")
        api_headers = {
            "Accept": "application/json, text/plain, */*",
            "Content-Type": "application/json;charset=utf-8",
            "Origin": base_url,
            "Referer": f"{base_url}/core/scheduler/appointments",
            "User-Agent": headers["User-Agent"],
            "X-Application-Key": headers["X-Application-Key"],
            "X-Origin": headers["X-Origin"],
            "X-XSRF-TOKEN": fresh_token
        }
        
        payload = {
            "StartDate": "5/1/2025, 12:00:00 AM",
            "EndDate": "5/31/2025, 11:59:59 PM",
            "LocationIds": [],
            "StaffIds": [],
            "ClientIds": [],
            "AppointmentTypeIds": [],
            "AppointmentStatusIds": [],
            "ServiceIds": [],
            "PayorIds": [],
            "IncludeNonBillableAppointments": True,
            "IncludeBlockedTime": False,
            "IncludeGroupAppointments": True,
            "IncludeRecurringAppointments": True,
            "IncludeAppointmentNotes": False,
            "IncludeProgressNotes": False,
            "IncludeClientDemographics": True,
            "IncludeStaffDemographics": True,
            "IncludeLocationDemographics": True,
            "IncludeServiceDemographics": True,
            "IncludePayorDemographics": True,
            "IncludeAppointmentTypeDemographics": True,
            "IncludeAppointmentStatusDemographics": True,
            "IncludeCustomFields": True,
            "IncludeClientCustomFields": True,
            "IncludeStaffCustomFields": True,
            "IncludeLocationCustomFields": True,
            "IncludeServiceCustomFields": True,
            "IncludePayorCustomFields": True,
            "IncludeAppointmentTypeCustomFields": True,
            "IncludeAppointmentStatusCustomFields": True
        }
        
        logger.info("Making API call to GetAppointmentsListPrintAsync")
        logger.debug(f"Headers: {api_headers}")
        
        resp5 = session.post(
            f"{base_url}/core/api/scheduling/scheduling/GetAppointmentsListPrintAsync",
            json=payload,
            headers=api_headers,
            timeout=30
        )
        
        logger.info(f"API call result: {resp5.status_code}")
        if resp5.status_code != 200:
            logger.error(f"API call failed: {resp5.text}")
            return False
        
        logger.info("Authentication and API call successful!")
        return True
        
    except Exception as e:
        logger.error(f"Authentication test failed: {e}")
        return False

if __name__ == "__main__":
    success = test_auth_flow()
    if success:
        print("✅ Authentication test passed")
    else:
        print("❌ Authentication test failed")
