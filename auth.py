#!/usr/bin/env python3
"""
Authentication module for Rethink BH API.
Handles login, session management, and token generation.
"""

import os
import logging
import requests
import re
from typing import Optional, Dict, Tuple
from google.cloud import secretmanager

# Configure module logger
logger = logging.getLogger(__name__)

# Security event logger for authentication events
security_logger = logging.getLogger(f"{__name__}.security")

class RethinkAuthError(Exception):
    """Custom exception for Rethink authentication operations."""
    pass

class RethinkAuth:
    """Handles authentication and session management for Rethink BH API."""
    
    def __init__(self):
        self.session = requests.Session()
        self.base_url = "https://webapp.rethinkbehavioralhealth.com"
        self.headers = {
            "Content-Type": "application/json;charset=utf-8",
            "Accept": "application/json, text/plain, */*",
            "X-Application-Key": "74569e11-18b4-4122-a58d-a4b830aa12c4",
            "X-Origin": "<PERSON>ular",
            "User-Agent": "Mozilla/5.0 (X11; Linux x86_64; rv:139.0) Gecko/139.0",
            "Origin": self.base_url,
            "Referer": f"{self.base_url}/Healthcare#/Login",
        }
        self._authenticated = False

    def _get_secret(self, secret_name: str, project_id: Optional[str] = None) -> str:
        """Retrieve secret from Google Cloud Secret Manager."""
        try:
            if project_id is None:
                project_id = os.getenv('GOOGLE_CLOUD_PROJECT')
                if not project_id:
                    raise RethinkAuthError("GOOGLE_CLOUD_PROJECT environment variable not set")
            
            client = secretmanager.SecretManagerServiceClient()
            name = f"projects/{project_id}/secrets/{secret_name}/versions/latest"
            response = client.access_secret_version(request={"name": name})
            return response.payload.data.decode("UTF-8")
        except Exception as e:
            logger.error(f"Failed to retrieve secret {secret_name}")
            raise RethinkAuthError(f"Secret retrieval failed: {str(e)}")

    def get_credentials(self) -> Tuple[str, str]:
        """Get credentials from environment or Secret Manager."""
        email = os.getenv("RTHINK_USER")
        password = os.getenv("RTHINK_PASS")

        if email and password:
            logger.debug("AUTH_CREDS - Using environment credentials")
            security_logger.info("Credentials loaded from environment variables")
            return email, password

        # Fallback to Secret Manager
        try:
            email = email or self._get_secret("RTHINK_USER")
            password = password or self._get_secret("RTHINK_PASS")
            logger.debug("AUTH_CREDS - Using Secret Manager credentials")
            security_logger.info("Credentials loaded from Google Secret Manager")
            return email, password
        except Exception as e:
            security_logger.error(f"CREDENTIAL_FAILURE - Failed to retrieve credentials: {e}")
            raise RethinkAuthError(f"Failed to get credentials: {e}")

    def _fetch_token(self) -> Optional[str]:
        """Extract anti-forgery token from session cookies."""
        for cookie in self.session.cookies:
            if any(k in cookie.name.upper() for k in ("XSRF", "ANTIFORGERY", "REQUESTVERIFICATIONTOKEN")):
                return cookie.value
        return None

    def _with_token(self, headers: dict) -> dict:
        """Add anti-forgery token to headers."""
        token = self._fetch_token()
        if not token:
            raise RethinkAuthError("No anti-forgery token found in cookies")
        return {**headers, "X-XSRF-TOKEN": token}

    def authenticate(self, email: str = None, password: str = None) -> None:
        """Authenticate with Rethink BH with comprehensive logging."""
        if self._authenticated:
            logger.debug("AUTH_SKIP - Already authenticated")
            return

        if not email or not password:
            email, password = self.get_credentials()

        # Log authentication attempt (without credentials)
        logger.info(f"AUTH_START - Authenticating with Rethink BH for user: {email[:3]}***")
        security_logger.info(f"Authentication attempt for user: {email}")

        try:
            # Authentication flow with step-by-step logging
            logger.debug("AUTH_STEP1 - Getting initial session")
            self.session.get(f"{self.base_url}/HealthCare", headers=self.headers).raise_for_status()

            logger.debug("AUTH_STEP2 - Getting authentication details")
            self.session.post(
                f"{self.base_url}/HealthCare/SingleSignOn/GetAuthenticationDetail",
                json={"User": email},
                headers=self._with_token(self.headers)
            ).raise_for_status()

            logger.debug("AUTH_STEP3 - Performing login")
            self.session.post(
                f"{self.base_url}/HealthCare/User/Login",
                json={"User": email, "Password": "***", "setPermissions": True},  # Never log password
                headers=self._with_token(self.headers)
            ).raise_for_status()

            logger.debug("AUTH_STEP4 - Accessing scheduler")
            self.session.get(
                f"{self.base_url}/core/scheduler/appointments",
                headers=self._with_token(self.headers)
            ).raise_for_status()

            self._authenticated = True
            logger.info("AUTH_SUCCESS - Authentication completed successfully")
            security_logger.info(f"Successful authentication for user: {email}")

        except requests.RequestException as e:
            logger.error(f"AUTH_FAILURE - Authentication failed: {e}")
            security_logger.warning(f"Failed authentication attempt for user: {email} - {e}")
            raise RethinkAuthError(f"Authentication failed: {e}")

    def get_api_headers(self, request_type: str = "dashboard") -> Dict[str, str]:
        """Get headers with fresh tokens for API requests."""
        if not self._authenticated:
            raise RethinkAuthError("Must authenticate first")

        # Get XSRF token from session
        xsrf_headers = self._with_token(self.headers)
        xsrf_token = xsrf_headers.get("X-XSRF-TOKEN", "")

        logger.debug(f"API_HEADERS - Generating headers for {request_type} request, XSRF token: {xsrf_token[:10]}..." if xsrf_token else "API_HEADERS - No XSRF token found")

        if request_type == "dashboard":
            # Dashboard requests need MVC token
            mvc_token = self._get_mvc_token(xsrf_headers)
            return {
                "Accept": "application/json, text/plain, */*",
                "Content-Type": "application/json;charset=utf-8",
                "Origin": self.base_url,
                "Referer": f"{self.base_url}/Healthcare",
                "User-Agent": self.headers["User-Agent"],
                "X-Application-Key": self.headers["X-Application-Key"],
                "X-Origin": self.headers["X-Origin"],
                "X-XSRF-TOKEN": xsrf_token,
                "X-XSRF-MVC-TOKEN": mvc_token
            }
        else:
            # Appointment/scheduler requests need Origin and Referer headers too
            return {
                "Accept": "application/json, text/plain, */*",
                "Content-Type": "application/json;charset=utf-8",
                "Origin": self.base_url,
                "Referer": f"{self.base_url}/core/scheduler/appointments",
                "User-Agent": self.headers["User-Agent"],
                "X-Application-Key": self.headers["X-Application-Key"],
                "X-Origin": self.headers["X-Origin"],
                "X-XSRF-TOKEN": xsrf_token
            }

    def _get_mvc_token(self, headers: Dict[str, str]) -> str:
        """Extract MVC token from dashboard page."""
        try:
            response = self.session.get(f"{self.base_url}/Healthcare/ReportingDashboard", headers=headers)
            if response.status_code != 200:
                logger.warning(f"Failed to get dashboard page: {response.status_code}")
                return headers.get("X-XSRF-TOKEN", "")

            # Extract token from HTML
            patterns = [
                r'name="__RequestVerificationToken"[^>]*value="([^"]+)"',
                r'"__RequestVerificationToken":"([^"]+)"'
            ]

            for pattern in patterns:
                match = re.search(pattern, response.text)
                if match:
                    token = match.group(1)
                    logger.debug("Retrieved MVC token")
                    return token

            logger.warning("MVC token not found, using XSRF token")
            return headers.get("X-XSRF-TOKEN", "")

        except Exception as e:
            logger.warning(f"Error getting MVC token: {e}")
            return headers.get("X-XSRF-TOKEN", "")

    def make_request(self, method: str, url: str, request_type: str = "dashboard", **kwargs) -> requests.Response:
        """Make authenticated request to Rethink BH API with retry logic."""
        if not self._authenticated:
            self.authenticate()

        # Visit appropriate pages based on request type
        if request_type == "scheduler":
            self._visit_scheduler_pages()
        elif request_type == "dashboard":
            self._visit_dashboard_pages()

        headers = self.get_api_headers(request_type)
        if 'headers' in kwargs:
            headers.update(kwargs['headers'])
        kwargs['headers'] = headers

        try:
            logger.debug(f"API_REQUEST - Making {method} request to {url}")
            response = self.session.request(method, url, **kwargs)
            response.raise_for_status()
            logger.debug(f"API_SUCCESS - {method} {url} returned {response.status_code}")
            return response
        except requests.RequestException as e:
            # If we get a 401, try re-authenticating once
            if hasattr(e, 'response') and e.response is not None and e.response.status_code == 401:
                logger.warning(f"API_RETRY - Got 401, attempting re-authentication for {method} {url}")
                try:
                    # Force re-authentication
                    self._authenticated = False
                    self.authenticate()

                    # Re-visit pages and get fresh headers
                    if request_type == "scheduler":
                        self._visit_scheduler_pages()
                    elif request_type == "dashboard":
                        self._visit_dashboard_pages()

                    headers = self.get_api_headers(request_type)
                    if 'headers' in kwargs:
                        headers.update(kwargs['headers'])
                    kwargs['headers'] = headers

                    # Retry the request
                    response = self.session.request(method, url, **kwargs)
                    response.raise_for_status()
                    logger.info(f"API_RETRY_SUCCESS - {method} {url} succeeded after re-authentication")
                    return response
                except Exception as retry_e:
                    logger.error(f"API_RETRY_FAILED - Retry failed for {method} {url}: {retry_e}")
                    raise RethinkAuthError(f"API request failed even after retry: {retry_e}")

            logger.error(f"API_REQUEST_FAILED - {method} {url} - {e}")
            raise RethinkAuthError(f"API request failed: {e}")

    def _visit_scheduler_pages(self) -> None:
        """Visit scheduler pages to establish session context."""
        try:
            logger.debug("AUTH_CONTEXT - Visiting scheduler pages to establish session context")

            # Visit the main scheduler page first
            self.session.get(
                f"{self.base_url}/core/scheduler/appointments",
                headers=self._with_token(self.headers)
            ).raise_for_status()

            # Also visit the scheduling section to ensure full context
            self.session.get(
                f"{self.base_url}/core/scheduler",
                headers=self._with_token(self.headers)
            ).raise_for_status()

            logger.debug("AUTH_CONTEXT - Scheduler session context established")

        except Exception as e:
            logger.warning(f"AUTH_CONTEXT_FAILED - Failed to visit scheduler pages: {e}")
            # Don't raise here, as this might still work

    def _visit_dashboard_pages(self) -> None:
        """Visit dashboard pages to establish session context."""
        try:
            logger.debug("Visiting dashboard pages")
            self.session.get(
                f"{self.base_url}/Healthcare",
                headers=self._with_token(self.headers)
            ).raise_for_status()
        except Exception as e:
            logger.warning(f"Failed to visit dashboard pages: {e}")

    @property
    def is_authenticated(self) -> bool:
        """Check if currently authenticated."""
        return self._authenticated
